'use client';

import Link from 'next/link';
import { usePathname } from 'next/navigation';

interface HeaderProps {
  title?: string;
  subtitle?: string;
  showBackButton?: boolean;
  backUrl?: string;
  backText?: string;
  rightActions?: React.ReactNode;
}

export default function Header({
  title = "Kesehatan Lansia",
  subtitle = "Posyandu Digital Modern",
  showBackButton = false,
  backUrl = "/",
  backText = "Beranda",
  rightActions
}: HeaderProps) {
  const pathname = usePathname();

  // Determine page-specific content
  const getPageContent = () => {
    if (pathname === '/') {
      return {
        title: "Kesehatan Lansia",
        subtitle: "Posyandu Digital Modern",
        showStatus: true
      };
    } else if (pathname === '/form') {
      return {
        title: "Kesehatan Lansia",
        subtitle: "Form Pendaftaran",
        showStatus: false
      };
    } else if (pathname === '/scan') {
      return {
        title: "Kesehat<PERSON>",
        subtitle: "Scan QR Code",
        showStatus: false
      };
    } else if (pathname === '/profiles') {
      return {
        title: "Kesehatan Lansia",
        subtitle: "Daftar Semua Lansia",
        showStatus: false
      };
    } else if (pathname.startsWith('/profile/')) {
      return {
        title: "Kesehatan Lansia",
        subtitle: subtitle || "Profil Lansia",
        showStatus: false
      };
    }
    return {
      title,
      subtitle,
      showStatus: false
    };
  };

  const pageContent = getPageContent();

  return (
    <header className="bg-white border-b border-gray-200 shadow-sm">
      <div className="container-responsive">
        <div className="flex justify-between items-center py-4 sm:py-6">
          {/* Left Section - Logo and Title */}
          <div className="flex items-center space-x-2 sm:space-x-4 min-w-0 flex-1">
            <Link href="/" className="flex items-center space-x-2 sm:space-x-3 min-w-0">
              <div className="w-10 h-10 sm:w-12 sm:h-12 bg-blue-600 rounded-2xl flex items-center justify-center shadow-sm flex-shrink-0">
                <svg className="w-5 h-5 sm:w-6 sm:h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                </svg>
              </div>
              <div className="min-w-0">
                <h1 className="text-lg sm:text-2xl font-bold text-gray-900 truncate">{pageContent.title}</h1>
                <p className="text-xs sm:text-sm text-gray-600 truncate">{pageContent.subtitle}</p>
              </div>
            </Link>
          </div>

          {/* Right Section - Actions and Navigation */}
          <div className="flex items-center space-x-2 sm:space-x-4 flex-shrink-0">
            {/* Status Indicator (only on home page) */}
            {pageContent.showStatus && (
              <div className="hidden md:flex items-center space-x-4">
                <span className="badge-success">
                  <span className="w-2 h-2 bg-green-400 rounded-full mr-2"></span>
                  Online
                </span>
              </div>
            )}

            {/* Custom Right Actions */}
            {rightActions}

            {/* Back Button */}
            {showBackButton && (
              <Link
                href={backUrl}
                className="btn-secondary text-xs sm:text-sm flex-shrink-0"
              >
                <span className="hidden sm:inline">← {backText}</span>
                <span className="sm:hidden">←</span>
              </Link>
            )}

            {/* Default Navigation for specific pages */}
            {!showBackButton && pathname !== '/' && (
              <Link
                href="/"
                className="text-gray-600 hover:text-gray-900 transition-colors text-xs sm:text-sm"
              >
                <span className="hidden sm:inline">← Beranda</span>
                <span className="sm:hidden">←</span>
              </Link>
            )}
          </div>
        </div>
      </div>
    </header>
  );
}
